import polars as pl

# Sample DataFrame
df = pl.DataFrame({"A": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "B": [10, 9, 8, 7, 6, 5, 4, 3, 2, 1]})

# Rolling sum with a window size of 3
df_rolling_sum = df.with_columns(
    rolling_sum=pl.col("A").rolling_sum(window_size=3)
)
print(df_rolling_sum)

# Rolling mean with a window size of 4 and a minimum of 2 valid values
df_rolling_mean = df.with_columns(
    rolling_mean=pl.col("A").rolling_mean(window_size=4, min_periods=2)
)
print(df_rolling_mean)

import polars as pl

# Create a sample dataframe
df = pl.DataFrame({
    "product_id": ["A1", "A2", "A3", "B1", "B2", "C1"],
    "category": ["electronics", "electronics", "electronics", "home_goods", "home_goods", "books"],
    "price": [1200, 800, 1500, 50, 75, 25]
})

print("Original DataFrame:")
print(df)

# Add a new column 'category_count'
df_with_count = df.with_columns(
    pl.col("product_id").count().over("category").alias("category_count")
)

print("\nDataFrame with Row Count per Category:")
print(df_with_count)

df = pl.read_csv("../data/all_idents_dynamic_dedup.csv", infer_schema=False)
print(df.shape)
df = df.with_columns(
    pl.col("Reference #").count().over("Reference #").alias("count")
).filter(pl.col("count") > 1)
print(df.shape)

df = pl.read_csv("../data/all_idents_simple_dedup.csv", infer_schema=False)
print(df.shape)
df = df.with_columns(
    pl.col("Reference #").count().over("Reference #").alias("count")
).filter(pl.col("count") > 1)
print(df.shape)

# Example of dropping duplicates in Polars
df = pl.read_csv("../data/all_idents_simple_dedup.csv", infer_schema=False)
print("Original shape:", df.shape)

# Drop duplicates
df_unique = df.unique(subset=["Reference #"], keep="first")
print("After dropping duplicates:", df_unique.shape)

# Alternative with different keep strategy
df_unique_last = df.unique(subset=["Reference #"], keep="last")
print("After dropping duplicates (keeping last):", df_unique_last.shape)

# Read the data
df = pl.read_csv("../data/all_idents_dynamic_dedup.csv", infer_schema=False)
print("Original shape:", df.shape)

# Drop duplicates based on "Reference #" column
df_unique = df.unique(subset=["Reference #"], keep="first")
print("After dropping duplicates:", df_unique.shape)

# Save the deduplicated data to a new CSV file
output_path = "../data/deduplicated_data.csv"
df_unique.write_csv(output_path)
print(f"Saved deduplicated data to {output_path}")

df_unique.columns

