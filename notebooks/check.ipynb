{"cells": [{"cell_type": "code", "execution_count": 1, "id": "3d65dca0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["shape: (10, 3)\n", "┌─────┬─────┬─────────────┐\n", "│ A   ┆ B   ┆ rolling_sum │\n", "│ --- ┆ --- ┆ ---         │\n", "│ i64 ┆ i64 ┆ i64         │\n", "╞═════╪═════╪═════════════╡\n", "│ 1   ┆ 10  ┆ null        │\n", "│ 2   ┆ 9   ┆ null        │\n", "│ 3   ┆ 8   ┆ 6           │\n", "│ 4   ┆ 7   ┆ 9           │\n", "│ 5   ┆ 6   ┆ 12          │\n", "│ 6   ┆ 5   ┆ 15          │\n", "│ 7   ┆ 4   ┆ 18          │\n", "│ 8   ┆ 3   ┆ 21          │\n", "│ 9   ┆ 2   ┆ 24          │\n", "│ 10  ┆ 1   ┆ 27          │\n", "└─────┴─────┴─────────────┘\n", "shape: (10, 3)\n", "┌─────┬─────┬──────────────┐\n", "│ A   ┆ B   ┆ rolling_mean │\n", "│ --- ┆ --- ┆ ---          │\n", "│ i64 ┆ i64 ┆ f64          │\n", "╞═════╪═════╪══════════════╡\n", "│ 1   ┆ 10  ┆ null         │\n", "│ 2   ┆ 9   ┆ 1.5          │\n", "│ 3   ┆ 8   ┆ 2.0          │\n", "│ 4   ┆ 7   ┆ 2.5          │\n", "│ 5   ┆ 6   ┆ 3.5          │\n", "│ 6   ┆ 5   ┆ 4.5          │\n", "│ 7   ┆ 4   ┆ 5.5          │\n", "│ 8   ┆ 3   ┆ 6.5          │\n", "│ 9   ┆ 2   ┆ 7.5          │\n", "│ 10  ┆ 1   ┆ 8.5          │\n", "└─────┴─────┴──────────────┘\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/kl/8qx3cn013cxg94p8xq1yy8nr0000gp/T/ipykernel_87024/2064698330.py:14: DeprecationWarning: the argument `min_periods` for `Expr.rolling_mean` is deprecated. It was renamed to `min_samples` in version 1.21.0.\n", "  rolling_mean=pl.col(\"A\").rolling_mean(window_size=4, min_periods=2)\n"]}], "source": ["import polars as pl\n", "\n", "# Sample DataFrame\n", "df = pl.Data<PERSON>rame({\"A\": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], \"B\": [10, 9, 8, 7, 6, 5, 4, 3, 2, 1]})\n", "\n", "# Rolling sum with a window size of 3\n", "df_rolling_sum = df.with_columns(\n", "    rolling_sum=pl.col(\"A\").rolling_sum(window_size=3)\n", ")\n", "print(df_rolling_sum)\n", "\n", "# Rolling mean with a window size of 4 and a minimum of 2 valid values\n", "df_rolling_mean = df.with_columns(\n", "    rolling_mean=pl.col(\"A\").rolling_mean(window_size=4, min_periods=2)\n", ")\n", "print(df_rolling_mean)"]}, {"cell_type": "code", "execution_count": 2, "id": "51e26101", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original DataFrame:\n", "shape: (6, 3)\n", "┌────────────┬─────────────┬───────┐\n", "│ product_id ┆ category    ┆ price │\n", "│ ---        ┆ ---         ┆ ---   │\n", "│ str        ┆ str         ┆ i64   │\n", "╞════════════╪═════════════╪═══════╡\n", "│ A1         ┆ electronics ┆ 1200  │\n", "│ A2         ┆ electronics ┆ 800   │\n", "│ A3         ┆ electronics ┆ 1500  │\n", "│ B1         ┆ home_goods  ┆ 50    │\n", "│ B2         ┆ home_goods  ┆ 75    │\n", "│ C1         ┆ books       ┆ 25    │\n", "└────────────┴─────────────┴───────┘\n", "\n", "DataFrame with Row Count per Category:\n", "shape: (6, 4)\n", "┌────────────┬─────────────┬───────┬────────────────┐\n", "│ product_id ┆ category    ┆ price ┆ category_count │\n", "│ ---        ┆ ---         ┆ ---   ┆ ---            │\n", "│ str        ┆ str         ┆ i64   ┆ u32            │\n", "╞════════════╪═════════════╪═══════╪════════════════╡\n", "│ A1         ┆ electronics ┆ 1200  ┆ 3              │\n", "│ A2         ┆ electronics ┆ 800   ┆ 3              │\n", "│ A3         ┆ electronics ┆ 1500  ┆ 3              │\n", "│ B1         ┆ home_goods  ┆ 50    ┆ 2              │\n", "│ B2         ┆ home_goods  ┆ 75    ┆ 2              │\n", "│ C1         ┆ books       ┆ 25    ┆ 1              │\n", "└────────────┴─────────────┴───────┴────────────────┘\n"]}], "source": ["import polars as pl\n", "\n", "# Create a sample dataframe\n", "df = pl.DataFrame({\n", "    \"product_id\": [\"A1\", \"A2\", \"A3\", \"B1\", \"B2\", \"C1\"],\n", "    \"category\": [\"electronics\", \"electronics\", \"electronics\", \"home_goods\", \"home_goods\", \"books\"],\n", "    \"price\": [1200, 800, 1500, 50, 75, 25]\n", "})\n", "\n", "print(\"Original DataFrame:\")\n", "print(df)\n", "\n", "# Add a new column 'category_count'\n", "df_with_count = df.with_columns(\n", "    pl.col(\"product_id\").count().over(\"category\").alias(\"category_count\")\n", ")\n", "\n", "print(\"\\nDataFrame with Row Count per Category:\")\n", "print(df_with_count)"]}, {"cell_type": "code", "execution_count": 14, "id": "1918ffd7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(341208, 14)\n", "(307568, 15)\n"]}], "source": ["df = pl.read_csv(\"../data/all_idents_dynamic_dedup.csv\", infer_schema=False)\n", "print(df.shape)\n", "df = df.with_columns(\n", "    pl.col(\"Reference #\").count().over(\"Reference #\").alias(\"count\")\n", ").filter(pl.col(\"count\") > 1)\n", "print(df.shape)"]}, {"cell_type": "code", "execution_count": 16, "id": "c98ea7a8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(184538, 14)\n", "(234, 15)\n"]}], "source": ["df = pl.read_csv(\"../data/all_idents_simple_dedup.csv\", infer_schema=False)\n", "print(df.shape)\n", "df = df.with_columns(\n", "    pl.col(\"Reference #\").count().over(\"Reference #\").alias(\"count\")\n", ").filter(pl.col(\"count\") > 1)\n", "print(df.shape)"]}, {"cell_type": "code", "execution_count": 21, "id": "4118ab8d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original shape: (184538, 14)\n", "After dropping duplicates: (184421, 14)\n", "After dropping duplicates (keeping last): (184421, 14)\n"]}], "source": ["# Example of dropping duplicates in Polars\n", "df = pl.read_csv(\"../data/all_idents_simple_dedup.csv\", infer_schema=False)\n", "print(\"Original shape:\", df.shape)\n", "\n", "# Drop duplicates\n", "df_unique = df.unique(subset=[\"Reference #\"], keep=\"first\")\n", "print(\"After dropping duplicates:\", df_unique.shape)\n", "\n", "# Alternative with different keep strategy\n", "df_unique_last = df.unique(subset=[\"Reference #\"], keep=\"last\")\n", "print(\"After dropping duplicates (keeping last):\", df_unique_last.shape)"]}, {"cell_type": "code", "execution_count": 23, "id": "31c6d530", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original shape: (341208, 14)\n", "After dropping duplicates: (187424, 14)\n", "Saved deduplicated data to ../data/deduplicated_data.csv\n"]}], "source": ["# Read the data\n", "df = pl.read_csv(\"../data/all_idents_dynamic_dedup.csv\", infer_schema=False)\n", "print(\"Original shape:\", df.shape)\n", "\n", "# Drop duplicates based on \"Reference #\" column\n", "df_unique = df.unique(subset=[\"Reference #\"], keep=\"first\")\n", "print(\"After dropping duplicates:\", df_unique.shape)\n", "\n", "# Save the deduplicated data to a new CSV file\n", "output_path = \"../data/deduplicated_data.csv\"\n", "df_unique.write_csv(output_path)\n", "print(f\"Saved deduplicated data to {output_path}\")"]}, {"cell_type": "code", "execution_count": 24, "id": "1d071c18", "metadata": {}, "outputs": [{"data": {"text/plain": ["['Reference #',\n", " 'DOB',\n", " 'SSN',\n", " 'First Name',\n", " 'Middle Name',\n", " 'Last Name',\n", " 'House #',\n", " 'Street Name',\n", " 'Str Suf',\n", " 'Unit',\n", " 'Unit #',\n", " 'City',\n", " 'State',\n", " 'ZIP Code']"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["df_unique.columns"]}, {"cell_type": "code", "execution_count": null, "id": "6c970108", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "integration-test-data", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}