"""
Synthetic Data Generator

This module generates synthetic application data based on seed data from CSV files.
It creates current applications and former applications with realistic variations.
"""

import random
import csv
from datetime import datetime, timedelta
from typing import List, Dict, Optional

from star.constant import Carrier, carrier_sales_channel_dict, carrier_channel_mapping
from star.timestamp import random_offset


def generate_random_phone() -> Optional[str]:
    """Generate a random phone number with 50% chance of being not null."""
    if random.random() >= 0.5:
        return None
    
    # Generate a 10-digit phone number
    area_code = random.randint(200, 999)
    exchange = random.randint(200, 999)
    number = random.randint(1000, 9999)
    return f"{area_code}{exchange:03d}{number:04d}"


def generate_random_email() -> Optional[str]:
    """Generate a random email with 20% chance of being not null."""
    if random.random() >= 0.2:
        return None
    
    domains = ["gmail.com", "yahoo.com", "hotmail.com", "outlook.com", "aol.com"]
    prefixes = ["user", "test", "demo", "sample", "john", "jane", "mike", "sarah"]
    
    prefix = random.choice(prefixes)
    suffix = random.randint(100, 9999)
    domain = random.choice(domains)
    
    return f"{prefix}{suffix}@{domain}"


def generate_random_timestamp_in_month(month: str) -> tuple:
    """Generate random timestamp within a given month (YYYYMM format)."""
    year = int(month[:4])
    month_num = int(month[4:6])
    
    # Calculate start and end of month
    start_date = datetime(year, month_num, 1)
    
    # Calculate end of month
    if month_num == 12:
        end_date = datetime(year + 1, 1, 1) - timedelta(days=1)
    else:
        end_date = datetime(year, month_num + 1, 1) - timedelta(days=1)
    
    # Generate random timestamp within the month
    time_diff = end_date - start_date
    random_seconds = random.randint(0, int(time_diff.total_seconds()))
    random_datetime = start_date + timedelta(seconds=random_seconds)
    
    # Format as ISO string
    iso_timestamp = random_datetime.strftime('%Y-%m-%dT%H:%M:%SZ')
    
    # Use star.timestamp.random_offset to get UTC timestamp and offset
    return random_offset(iso_timestamp)


def generate_curr_app(row: Dict[str, str], month: str) -> List[str]:
    """
    Generate current application data based on CSV row and month.
    
    Args:
        row: Dictionary containing CSV row data with keys matching CSV headers
        month: Month in YYYYMM format
        
    Returns:
        List of strings representing the application data
    """
    # Extract basic info from CSV
    reference_number = row['Reference #']
    pin = reference_number[-10:] if len(reference_number) >= 10 else reference_number
    app_id = f"{pin}_0"
    
    # Personal information
    first_name = row['First Name']
    last_name = row['Last Name']
    
    # Handle middle name - use first letter if exists, otherwise empty
    middle_name = row['Middle Name']
    if middle_name and middle_name.strip():
        middle_name = middle_name.strip()[0]
    else:
        middle_name = ""
    
    dob = row['DOB']
    ssn = row['SSN']
    
    # Address information
    street_num = row['House #']
    street_name = row['Street Name']
    street_suffix = row['Str Suf']
    unit = row['Unit']
    unit_number = row['Unit #']
    city = row['City']
    state = row['State']
    zip_code = row['ZIP Code']
    
    # Generate random contact info
    phone_number = generate_random_phone() or ""
    email = generate_random_email() or ""
    
    # Generate timestamps
    app_timestamp, app_utc_timestamp, app_offset = generate_random_timestamp_in_month(month)
    
    # Generate client and sales channel info
    client_name = random.choice(list(Carrier)).value
    available_channels = carrier_sales_channel_dict[Carrier(client_name)]
    sales_channel = random.choice(available_channels)
    
    # Get mapped sales channel
    mapping_key = (client_name, sales_channel)
    mapped_sales_channel = carrier_channel_mapping.get(mapping_key, -1)
    
    return [
        app_id,
        reference_number,
        pin,
        first_name,
        middle_name,
        last_name,
        dob,
        ssn,
        street_num,
        street_name,
        street_suffix,
        unit,
        unit_number,
        city,
        state,
        zip_code,
        phone_number,
        email,
        app_timestamp,
        app_utc_timestamp,
        app_offset,
        sales_channel,
        client_name,
        str(mapped_sales_channel)
    ]


def generate_former_apps(cur_app_row: List[str], month: str) -> List[List[str]]:
    """
    Generate 0 to 5 former applications based on current application.

    Args:
        cur_app_row: Current application data from generate_curr_app
        month: Current month in YYYYMM format

    Returns:
        List of former application records (each record is a list of strings)
    """
    # Randomly decide how many former apps to generate (0-5)
    if random.randint(0, 1) == 0:
        return []


    num_former_apps = random.randint(1, 5)
    former_apps = []

    # Parse current app data
    pin = cur_app_row[2]
    current_timestamp = cur_app_row[17]  # app_timestamp
    current_client = cur_app_row[21]     # client_name
    current_sales_channel = cur_app_row[20]  # sales_channel
    current_phone = cur_app_row[15]      # phone_number
    current_email = cur_app_row[16]      # email

    # Parse current timestamp to get the datetime object
    current_dt = datetime.strptime(current_timestamp, '%Y-%m-%dT%H:%M:%SZ')

    # Calculate month range (7 months back from current month)
    year = int(month[:4])
    month_num = int(month[4:6])

    start_year = year
    start_month = month_num - 6
    if start_month <= 0:
        start_year -= 1
        start_month += 12

    # Generate former applications
    for seq in range(1, num_former_apps + 1):
        # Copy current app data
        former_app = cur_app_row.copy()

        # Update app_id with sequence number
        former_app[0] = f"{pin}_{seq}"

        # Generate timestamp that is before current timestamp and within 7-month window
        # Calculate the earliest possible date (7 months back)
        earliest_date = datetime(start_year, start_month, 1)

        # Use current timestamp as the latest possible date
        latest_date = current_dt - timedelta(hours=1)  # At least 1 hour before current

        # Ensure we don't go before the 7-month window
        if latest_date < earliest_date:
            latest_date = earliest_date + timedelta(days=1)

        # Generate random timestamp between earliest_date and latest_date
        time_diff = latest_date - earliest_date
        if time_diff.total_seconds() > 0:
            random_seconds = random.randint(0, int(time_diff.total_seconds()))
            random_datetime = earliest_date + timedelta(seconds=random_seconds)
        else:
            random_datetime = earliest_date

        # Format as ISO string and get offset
        iso_timestamp = random_datetime.strftime('%Y-%m-%dT%H:%M:%SZ')
        app_timestamp, app_utc_timestamp, app_offset = random_offset(iso_timestamp)

        former_app[17] = app_timestamp      # app_timestamp
        former_app[18] = app_utc_timestamp  # app_utc_timestamp
        former_app[19] = app_offset         # app_offset

        # Phone number changes: 10% chance to change, 5% chance to be null
        phone_change_chance = random.random()
        if phone_change_chance < 0.05:  # 5% chance to be null
            former_app[15] = ""
        elif phone_change_chance < 0.15:  # 10% chance to change (5% + 10% = 15% total)
            former_app[15] = generate_random_phone() or ""
        else:
            # Keep current phone number
            former_app[15] = current_phone

        # Email changes: 10% chance to change, 5% chance to be null
        email_change_chance = random.random()
        if email_change_chance < 0.05:  # 5% chance to be null
            former_app[16] = ""
        elif email_change_chance < 0.15:  # 10% chance to change (5% + 10% = 15% total)
            former_app[16] = generate_random_email() or ""
        else:
            # Keep current email
            former_app[16] = current_email

        # Client name changes: 20% chance to change
        client_changed = False
        if random.random() < 0.2:
            # Choose a different client from the current one
            available_clients = [c.value for c in Carrier if c.value != current_client]
            if available_clients:
                new_client = random.choice(available_clients)
                former_app[21] = new_client
                client_changed = True
            else:
                # If somehow no other clients available, keep current
                former_app[21] = current_client
        else:
            # Keep current client
            former_app[21] = current_client

        # Sales channel changes: 10% chance to change, or if client changed
        if random.random() < 0.1 or client_changed:
            client_for_channel = former_app[21]
            available_channels = carrier_sales_channel_dict[Carrier(client_for_channel)]
            # Try to choose a different sales channel from current if possible
            if client_changed or len(available_channels) > 1:
                other_channels = [ch for ch in available_channels if ch != current_sales_channel]
                if other_channels:
                    new_sales_channel = random.choice(other_channels)
                else:
                    new_sales_channel = random.choice(available_channels)
            else:
                new_sales_channel = random.choice(available_channels)

            former_app[20] = new_sales_channel

            # Update mapped sales channel
            mapping_key = (client_for_channel, new_sales_channel)
            mapped_sales_channel = carrier_channel_mapping.get(mapping_key, -1)
            former_app[22] = str(mapped_sales_channel)
        else:
            # Keep current sales channel and client
            former_app[20] = current_sales_channel
            former_app[21] = current_client
            # Keep current mapped sales channel
            mapping_key = (current_client, current_sales_channel)
            mapped_sales_channel = carrier_channel_mapping.get(mapping_key, -1)
            former_app[22] = str(mapped_sales_channel)

        former_apps.append(former_app)

    return former_apps


def process_csv_file(file_path: str, month: str, output_file: str = None):
    """
    Process a CSV file and generate synthetic data for all rows.

    Args:
        file_path: Path to the input CSV file
        month: Month in YYYYMM format
        output_file: Optional output file path for results
    """
    results = []

    with open(file_path, 'r', newline='', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)

        for row in reader:
            # Generate current application
            curr_app = generate_curr_app(row, month)
            results.append(curr_app)

            # Generate former applications
            former_apps = generate_former_apps(curr_app, month)
            results.extend(former_apps)

    if output_file:
        # Write results to output file
        header = [
            'app_id', 'reference_number', 'pin', 'first_name', 'middle_name', 'last_name',
            'dob', 'ssn', 'street_num', 'street_name', 'street_suffix', 'unit', 'unit_number',
            'city', 'state', 'zip_code', 'phone_number', 'email',
            'app_timestamp', 'app_utc_timestamp', 'app_offset',
            'sales_channel', 'client_name', 'mapped_sales_channel'
        ]

        with open(output_file, 'w', newline='', encoding='utf-8') as outfile:
            writer = csv.writer(outfile)
            writer.writerow(header)
            writer.writerows(results)

    return results


def main():
    """Main function to process the actual CSV file and generate synthetic data."""
    import argparse
    import time

    parser = argparse.ArgumentParser(description='Generate synthetic application data from CSV')
    parser.add_argument('--input', '-i', default='data/deduplicated_data.csv',
                       help='Input CSV file path (default: data/deduplicated_data.csv)')
    parser.add_argument('--output', '-o', default='synthetic_applications.csv',
                       help='Output CSV file path (default: synthetic_applications.csv)')
    parser.add_argument('--month', '-m', default='202407',
                       help='Month in YYYYMM format (default: 202407)')
    parser.add_argument('--limit', '-l', type=int, default=None,
                       help='Limit number of input rows to process (for testing)')
    parser.add_argument('--test', action='store_true',
                       help='Run test mode with sample data instead of processing CSV')

    args = parser.parse_args()

    if args.test:
        # Run test mode with sample data
        test_rows = [
            {
                'Reference #': 'P160167593003',
                'DOB': '1985-03-15',
                'SSN': '666490644',
                'First Name': 'DONALD',
                'Middle Name': 'JAMES',
                'Last Name': 'BROOKS',
                'House #': '524',
                'Street Name': 'CINNABAR',
                'Str Suf': 'DR',
                'Unit': '',
                'Unit #': '',
                'City': 'LIVERMORE',
                'State': 'CA',
                'ZIP Code': '945505122'
            },
            {
                'Reference #': 'P180201133838',
                'DOB': '1961-05-20',
                'SSN': '666265590',
                'First Name': 'GEORGIA',
                'Middle Name': '',
                'Last Name': 'CINECEROS',
                'House #': '4311',
                'Street Name': 'GREENBUSH',
                'Str Suf': 'AVE',
                'Unit': '',
                'Unit #': '',
                'City': 'SHERMAN OAKS',
                'State': 'CA',
                'ZIP Code': '914233910'
            }
        ]

        print("Testing generate_curr_app function")
        print("=" * 50)

        field_names = [
            'app_id', 'reference_number', 'pin', 'first_name', 'middle_name', 'last_name',
            'dob', 'ssn', 'street_num', 'street_name', 'street_suffix', 'unit', 'unit_number',
            'city', 'state', 'zip_code', 'phone_number', 'email',
            'app_timestamp', 'app_utc_timestamp', 'app_offset',
            'sales_channel', 'client_name', 'mapped_sales_channel'
        ]

        for i, test_row in enumerate(test_rows, 1):
            print(f"\nTest Row {i}: {test_row['First Name']} {test_row['Last Name']}")
            print("-" * 40)

            curr_app = generate_curr_app(test_row, args.month)

            for field, value in zip(field_names, curr_app):
                print(f"  {field:20}: {value}")

        print(f"\nGenerated {len(test_rows)} current applications successfully!")
        return

    # Process actual CSV file
    print(f"Processing CSV file: {args.input}")
    print(f"Output file: {args.output}")
    print(f"Month: {args.month}")
    if args.limit:
        print(f"Limiting to first {args.limit} rows")
    print("=" * 50)

    start_time = time.time()

    try:
        # Check if input file exists
        import os
        if not os.path.exists(args.input):
            print(f"Error: Input file '{args.input}' not found!")
            return

        # Process the CSV file with optional row limit
        results = []
        row_count = 0

        with open(args.input, 'r', newline='', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)

            for row in reader:
                if args.limit and row_count >= args.limit:
                    break

                # Generate current application
                curr_app = generate_curr_app(row, args.month)
                results.append(curr_app)

                # Generate former applications
                former_apps = generate_former_apps(curr_app, args.month)
                results.extend(former_apps)

                row_count += 1

                # Progress indicator
                if row_count % 1000 == 0:
                    print(f"Processed {row_count} rows, generated {len(results)} applications...")

        # Write results to output file
        header = [
            'app_id', 'reference_number', 'pin', 'first_name', 'middle_name', 'last_name',
            'dob', 'ssn', 'street_num', 'street_name', 'street_suffix', 'unit', 'unit_number',
            'city', 'state', 'zip_code', 'phone_number', 'email',
            'app_timestamp', 'app_utc_timestamp', 'app_offset',
            'sales_channel', 'client_name', 'mapped_sales_channel'
        ]

        with open(args.output, 'w', newline='', encoding='utf-8') as outfile:
            writer = csv.writer(outfile)
            writer.writerow(header)
            writer.writerows(results)

        end_time = time.time()
        processing_time = end_time - start_time

        print(f"\nProcessing completed!")
        print(f"Input rows processed: {row_count}")
        print(f"Total applications generated: {len(results)}")
        print(f"Processing time: {processing_time:.2f} seconds")
        print(f"Output saved to: {args.output}")

    except Exception as e:
        print(f"Error processing file: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
