from datetime import datetime, timedelta
import random
import time


def random_timestamp(month_start: str, month_end: str) -> str:
    """Generate a random timestamp between month_start and month_end.
    
    Args:
        month_start: Start date in format 'YYYY-MM-DD'
        month_end: End date in format 'YYYY-MM-DD'
        
    Returns:
        Random timestamp in format 'YYYY-MM-DDThh:mm:ssZ'
    """
    
    start = datetime.strptime(month_start, '%Y-%m-%d')
    end = datetime.strptime(month_end, '%Y-%m-%d')
    
    start_ts = int(time.mktime(start.timetuple()))
    end_ts = int(time.mktime(end.timetuple()))
    
    random_ts = random.randint(start_ts, end_ts)
    random_dt = datetime.fromtimestamp(random_ts)
    
    return random_dt.strftime('%Y-%m-%dT%H:%M:%SZ')


offset_list = ["-10:00", "-09:00", "-08:00", "-07:00", "-06:00", "-05:00"]
def random_offset(ts_str):
    offset = random.choice(offset_list)
    offset_hour = int(offset[1:3])

    ts = datetime.strptime(ts_str, "%Y-%m-%dT%H:%M:%SZ")
    new_ts = ts - timedelta(hours=offset_hour)
    return ts_str, new_ts.strftime("%Y-%m-%dT%H:%M:%S"), offset

