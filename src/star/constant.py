from enum import Enum


class Carrier(str, Enum):
    TMB = "T-Mobile"
    USC = "US Cellular"
    VZW = "Verizon"
    ATT = "AT&T"


carrier_sales_channel_dict = {
    Carrier.TMB: [
        "Web",
        "TPR",
        "Retail",
        "Care",
        "Telesales",
        "Other",
        "Test"
    ],
    Carrier.USC: [
        "Company-Owned",
        "Exclusive Agent",
        "Web_App",
        "Walmart Postpaid",
        "Internal Telesales",
        "External Telesales",
        "Customer Care",
        "Non-Exclusive Agent",
        "Inside Sales",
        "Exclusive Walmart",
        "Business Direct",
        "Sam's Club Postpaid",
        "Walmart Prepaid",
        "Test"
    ],
    Carrier.VZW: [
        "Internet",
        "Local Retailer",
        "Communications Store Insi",
        "Telemarketing Inbound",
        "National Retailer",
        "Non-Commissionable",
        "Local Agent",
        "Indirect Internet",
        "Direct Sales",
        "Door to Door",
        "Retail - Mid Major Agents",
        "?",
        "UNSEEN",
        None
    ],
    Carrier.ATT: [
        "AT&T Retail",
        "Authorized Retail",
        "National Retail",
        "Sales & Service Centers",
        "DAP",
        "National Distribution",
        "Business",
        "Local Channel Ptnr",
        "Other",
        "Unknown",
        "OTHER/UNKNOWN",
        "UNKNOWN"
    ]
}

carrier_channel_mapping = {
    ("T-Mobile", "Web"): 0,
    ("T-Mobile", "TPR"): 1,
    ("T-Mobile", "Retail"): 2,
    ("T-Mobile", "Care"): 3,
    ("T-Mobile", "Telesales"): 4,
    ("T-Mobile", "Other"): 5,
    ("US Cellular", "Company-Owned"): 6,
    ("US Cellular", "Exclusive Agent"): 7,
    ("US Cellular", "Web_App"): 0,
    ("US Cellular", "Walmart Postpaid"): 8,
    ("US Cellular", "Internal Telesales"): 9,
    ("US Cellular", "External Telesales"): 10,
    ("US Cellular", "Customer Care"): 11,
    ("US Cellular", "Non-Exclusive Agent"): 12,
    ("US Cellular", "Inside Sales"): 12,
    ("US Cellular", "Exclusive Walmart"): 12,
    ("US Cellular", "Business Direct"): 12,
    ("US Cellular", "Sam's Club Postpaid"): 12,
    ("US Cellular", "Walmart Prepaid"): 12,
    ("Verizon", "Internet"): 0,
    ("Verizon", "Local Retailer"): 13,
    ("Verizon", "Communications Store Insi"): 14,
    ("Verizon", "Telemarketing Inbound"): 15,
    ("Verizon", "National Retailer"): 16,
    ("Verizon", "Non-Commissionable"): 17,
    ("Verizon", "Local Agent"): 18,
    ("Verizon", "Indirect Internet"): 19,
    ("Verizon", "Direct Sales"): 20,
    ("Verizon", "Door to Door"): 21,
    ("Verizon", "Retail - Mid Major Agents"): 22,
    ("Verizon", "?"): -1,
    ("AT&T", "Online"): 0,
    ("AT&T", "AT&T Retail"): 24,
    ("AT&T", "Authorized Retail"): 25,
    ("AT&T", "National Retail"): 26,
    ("AT&T", "Sales & Service Centers"): 27,
    ("AT&T", "DAP"): 28,
    ("AT&T", "National Distribution"): 29,
    ("AT&T", "Business"): 30,
    ("AT&T", "Local Channel Ptnr"): 31,
    ("AT&T", "Other"): 32,
    ("AT&T", "Unknown"): -1,
    ("AT&T", "OTHER/UNKNOWN"): -1,
}

id_types = [
    'DRIVER_LICENSE', 'IDCARD', 'PASSPORT', 'MILITARY_ID', '', 'TEST', None
]
