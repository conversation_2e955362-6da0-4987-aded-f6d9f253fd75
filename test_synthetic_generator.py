#!/usr/bin/env python3
"""
Test script for the synthetic data generator.
"""

import sys
import os
sys.path.append('src')

from synthetic_data_generator import generate_curr_app, generate_former_apps, process_csv_file


def test_single_row():
    """Test with a single row of data."""
    print("Testing single row generation...")
    
    # Sample row data matching the CSV structure
    test_row = {
        'Reference #': 'P160167593003',
        'DOB': '1985-03-15',
        'SSN': '666490644',
        'First Name': 'DONALD',
        'Middle Name': 'JAMES',
        'Last Name': 'BROOKS',
        'House #': '524',
        'Street Name': 'CINNABAR',
        'Str Suf': 'DR',
        'Unit': '',
        'Unit #': '',
        'City': 'LIVERMORE',
        'State': 'CA',
        'ZIP Code': '945505122'
    }
    
    month = "202407"  # July 2024
    
    # Generate current application
    print("\nGenerating current application...")
    curr_app = generate_curr_app(test_row, month)
    
    print("Current Application Fields:")
    field_names = [
        'app_id', 'reference_number', 'pin', 'first_name', 'middle_name', 'last_name',
        'dob', 'ssn', 'street_name', 'street_suffix', 'unit', 'unit_number',
        'city', 'state', 'zip_code', 'phone_number', 'email',
        'app_timestamp', 'app_utc_timestamp', 'app_offset',
        'sales_channel', 'client_name', 'mapped_sales_channel'
    ]
    
    for i, (field, value) in enumerate(zip(field_names, curr_app)):
        print(f"  {field}: {value}")
    
    # Generate former applications
    print("\nGenerating former applications...")
    former_apps = generate_former_apps(curr_app, month)
    
    print(f"Generated {len(former_apps)} former applications:")
    for i, app in enumerate(former_apps):
        print(f"  Former App {i+1} - app_id: {app[0]}, timestamp: {app[17]}")
    
    return curr_app, former_apps


def test_csv_processing():
    """Test processing a small subset of the CSV file."""
    print("\n" + "="*50)
    print("Testing CSV file processing...")
    
    # Process just a few rows from the actual CSV file
    try:
        results = process_csv_file('data/deduplicated_data.csv', '202407', 'test_output.csv')
        print(f"Processed CSV and generated {len(results)} total applications")
        print("Output saved to test_output.csv")
        
        # Show first few results
        print("\nFirst 3 applications:")
        for i, app in enumerate(results[:3]):
            print(f"  App {i+1}: {app[0]} - {app[3]} {app[5]} - {app[17]}")
            
    except Exception as e:
        print(f"Error processing CSV: {e}")


if __name__ == "__main__":
    print("Synthetic Data Generator Test")
    print("="*50)
    
    # Test single row
    curr_app, former_apps = test_single_row()
    
    # Test CSV processing
    test_csv_processing()
    
    print("\nTest completed!")
