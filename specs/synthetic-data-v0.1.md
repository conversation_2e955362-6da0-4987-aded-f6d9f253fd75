# Synethetic Data Generator
This project is aim to using star data to generate synthetic data

## Raw File
data/deduplicated_data.csv. But the code should be able to read any csv file with the same schema. The csv has header.

## Implementation Steps
For each row, it contains these fields 
```
[
    'Reference #',
    'DOB',
    'SSN',
    'First Name',
    'Middle Name',
    'Last Name',
    'House #',
    'Street Name',
    'Str Suf',
    'Unit',
    'Unit #',
    'City',
    'State',
    'ZIP Code'
 ]
 ```
 We want to use the row as the seed to expand more synthetic data. 

Here are the funtions we want to generate:
1. generate_curr_app(row, month):
   - based on the csv row and, we want to append some more fields, the final output should be a list of strings.
   - the output should contain these fields:
   ```
   [
        'app_id',
        'reference_number',
        'pin',
        
        'first_name',
        'middle_name',
        'last_name',
        
        'dob',
        
        'ssn',
        
        'street_name',
        'street_suffix',
        'unit',
        'unit_number',
        'city',
        'state',
        'zip_code',

        'phone_number',
        'email',

        'app_timestmap',
        'app_utc_timestamp',
        'app_offset',

        'sales_channel',
        'client_name'
   ]
   ```
   - The descriptions are:
     - app_id: pin + '_' + seq, it would be 0 for the current function
     - reference_number: from the csv
     - pin: last 10 characters of reference_number
     - first_name, last_name: from the csv
     - middle_name: from the csv, or the first letter of the middle name from csv
     - dob: from the csv
     - ssn: from the csv
     - street_name, street_suffix, unit, unit_number, city, state, zip_code: from the csv
     - phone_number: random phone number, 50% chance to be null
     - email: random email, 20% chance to be null
     - app_timestmap, app_utc_timestamp, app_offset: random timestamp within the month. Use function in star.timestamp
     - client_name: random client name based on the star.constant.Carrier
     - sales_channel: random sales channel based on the star.constant.carrier_sales_channel_dict and client_name
     - mapped_sales_channel: mappend sales channel using star.constant.carrier_channel_mapping and client_name and sales_channel

2. generate_former_apps(cur_app_row, month)
   - use the output from generate_curr_app as input
   - generate 0 to 5 former applications randomly
   - The output should be the same as the generate_curr_app, but the app_id should be pin + '_' + seq, where seq is 1, 2, 3, 4, 5
   - The app_timestmap, app_utc_timestamp, app_offset should be before the cur_app_row's app_timestmap, and the month should be within 7 months. For example, if month is 202407, all the former applications should be within 202401 to 202407.
   - phone_number, emails has 10% chance to be changed, and another 5% to be null
   - client_name has 20% chance to be changed
   - sales_channel has 10% chance to be changed, and if client_name is changed, sales_channel should also be changed
   - mapped_sales_channel should be changed accordingly
